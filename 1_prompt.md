# Prompt: Refactor Homepage Header

## A. The Objective & Context

The goal of this task is to replace the existing date header on the main screen (seen in `60.jpg`) with a new, more informative header based on the design in `image_e4a006.png`.

This is the first step in a larger UI overhaul. For this task, **only the header component should be changed**. The rest of the habit list layout should remain as it is. The new header will provide an at-a-glance summary of the user's weekly progress.

All styling for the new components (colors, typography, spacing) must be implemented according to the specifications in `style_habits9.md`.

## B. Detailed Implementation Plan

### 1. Remove the Old Date Header
- The existing header, which shows a horizontal list of dates, must be completely removed from the main screen's layout file.

### 2. Implement the New Header Layout
- Create a new, self-contained header view that will be placed at the top of the main screen.
- The layout should be structured as follows:
    - **Left Side**: A `TextView` displaying the static text "This Week".
    - **Center-Left**: A circular progress indicator view next to the "This Week" title. This view must display the overall weekly completion percentage as text in its center.
    - **Right Side**: A `TextView` that dynamically displays the current week (e.g., "Week 32").
    - **Navigation Arrows**: Place two icon buttons for navigating weeks: one to the left of the week number and one to the right. Use standard left and right chevron/arrow icons.

### 3. Implement Header Logic
- **Weekly Completion Percentage**: The circular progress indicator must be bound to the data source. It should calculate the total percentage of completed habits for all visible habits for the currently displayed week and update in real-time as habits are checked or unchecked.
- **Week Number Display**: The logic must correctly calculate and display the current week of the year.
- **Week Navigation**:
    - The left arrow button, when tapped, must load the data for the previous week.
    - The right arrow button, when tapped, must load the data for the next week.
    - Upon navigation, both the week number text and the completion percentage must update to reflect the newly displayed week's data.

### 4. Apply Styling from the Design System
- **Typography**:
    - The "This Week" title should use the `display-small` style from `style_habits9.md`.
    - The "Week 32" text and the percentage text inside the progress circle should use the `label-small` style.
- **Colors**:
    - All text, icons, and the progress bar must use the color tokens defined in `style_habits9.md` (e.g., `text-primary`, `accent-primary`).
    - The implementation must correctly handle both light and dark themes.

## C. Meticulous Verification Plan

1.  **Visual Layout Verification**:
    - **CRITICAL**: Confirm the new header is displayed at the top of the screen and its layout (title, progress circle, week number, arrows) exactly matches the header portion of `image_e4a006.png`.
    - Verify the old date header is completely gone.

2.  **Data Accuracy Verification**:
    - **CRITICAL**: Check that the percentage shown in the circular progress bar is an accurate calculation of all completed habits for the currently visible week.
    - To test, manually complete a habit for the current week and verify that the percentage immediately and correctly updates.
    - Confirm that the correct week number for the current date is displayed.

3.  **Navigation Functionality Verification**:
    - Tap the right navigation arrow. CRITICAL: The week number must update to the next week (e.g., "Week 33"), and the completion percentage must recalculate for that week. The habit list below should also refresh to show the data for that week.
    - Tap the left navigation arrow. CRITICAL: The week number must update to the previous week (e.g., "Week 31"), and all data must update accordingly.
    - Navigate back and forth several times to ensure there are no crashes or state inconsistencies.

4.  **Theme Verification**:
    - Switch the device between light and dark modes.
    - **CRITICAL**: Verify that every element in the new header (all text, icons, progress bar color) correctly adapts to the theme, using the specific colors defined in `style_habits9.md`.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.