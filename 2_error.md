Task :app:compileDebugKotlin FAILED
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:646:142 Argument type mismatch: actual type is 'kotlin.coroutines.SuspendFunction6<kotlin.Array<T>, ERROR CLASS: Cannot infer type for parameter completionsMap, ERROR CLASS: Cannot infer type for parameter completionValuesMap, ERROR CLASS: Cannot infer type for parameter dialogState, ERROR CLASS: Cannot infer type for parameter firstDayOfWeek, ERROR CLASS: Cannot infer type for parameter <unused var>, R>', but 'kotlin.coroutines.SuspendFunction1<kotlin.Array<T>, R>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:646:155 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:646:171 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:646:192 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:646:205 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:646:221 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:647:27 Unresolved reference 'habitsWithCompletions'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:648:27 Unresolved reference 'copy'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:650:56 Unresolved reference 'showMeasurableDialog'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:651:59 Unresolved reference 'measurableDialogHabitId'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:652:56 Unresolved reference 'measurableDialogDate'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:653:61 Unresolved reference 'measurableDialogHabitName'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:654:56 Unresolved reference 'measurableDialogUnit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:655:64 Unresolved reference 'measurableDialogCurrentValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:656:63 Unresolved reference 'measurableDialogTargetValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:657:62 Unresolved reference 'measurableDialogTargetType'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:660:48 Unresolved reference 'habitsWithCompletions'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:660:76 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:661:75 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:661:88 Not enough information to infer type argument for 'K'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:661:88 Not enough information to infer type argument for 'V'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:662:85 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:662:98 Not enough information to infer type argument for 'K'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:662:98 Not enough information to infer type argument for 'V'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:665:90 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:665:124 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:666:82 Unresolved reference 'keys'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:667:93 Unresolved reference 'keys'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:670:46 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:671:35 Unresolved reference 'weekInfo'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:674:46 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:675:25 Argument type mismatch: actual type is 'kotlin.Any', but 'kotlin.collections.Map<kotlin.Long, kotlin.Boolean>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:676:25 Argument type mismatch: actual type is 'kotlin.Any', but 'kotlin.collections.Map<kotlin.Long, kotlin.String>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:677:35 Unresolved reference 'weekInfo'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:679:42 Unresolved reference 'copy'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:688:60 Argument type mismatch: actual type is 'kotlinx.coroutines.flow.Flow<ERROR CLASS: Cannot infer argument for type parameter R>', but 'kotlin.collections.List<com.example.habits9.ui.HabitWithCompletions>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:690:27 Unresolved reference 'copy'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:693:56 Unresolved reference 'showMeasurableDialog'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:694:59 Unresolved reference 'measurableDialogHabitId'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:695:56 Unresolved reference 'measurableDialogDate'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:696:61 Unresolved reference 'measurableDialogHabitName'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:697:56 Unresolved reference 'measurableDialogUnit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:698:64 Unresolved reference 'measurableDialogCurrentValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:699:63 Unresolved reference 'measurableDialogTargetValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:700:62 Unresolved reference 'measurableDialogTargetType'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/home/<USER>'mainViewModel'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/home/<USER>'mainViewModel'.

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 17s
30 actionable tasks: 2 executed, 4 from cache, 24 up-to-date
