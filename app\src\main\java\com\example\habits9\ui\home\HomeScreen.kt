package com.example.habits9.ui.home

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.habits9.ui.components.SortMenuButton
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import com.example.habits9.ui.components.NumericalInputDialog

// Dark theme colors from style guide
val BackgroundDark = Color(0xFF121826)
val TextPrimary = Color(0xFFE2E8F0)
val TextSecondary = Color(0xFFA0AEC0)
val AccentPrimary = Color(0xFF81E6D9) // accent-primary
val DividerColor = Color(0xFF2D3748) // divider
val SurfaceVariantDark = Color(0xFF1A202C)

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    mainViewModel: com.example.habits9.ui.MainViewModel = hiltViewModel(),
    onNavigateToCreateHabit: () -> Unit = {},
    onNavigateToHabitDetails: (Long) -> Unit = {},
    onNavigateToSettings: () -> Unit = {},
    onNavigateToManageSections: () -> Unit = {},
    onNavigateToHabitReorder: () -> Unit = {}
) {
    val uiState by mainViewModel.enhancedUiState.collectAsState()

    Scaffold(
        topBar = {
            CustomHeader(
                currentSortType = uiState.currentSortType,
                onAddHabitClick = onNavigateToCreateHabit,
                onSettingsClick = onNavigateToSettings,
                onManageSectionsClick = onNavigateToManageSections,
                onSortTypeSelected = { sortType ->
                    if (sortType == com.example.habits9.data.HabitSortType.CUSTOM_ORDER) {
                        onNavigateToHabitReorder()
                    } else {
                        mainViewModel.updateSortType(sortType)
                    }
                }
            )
        },
        containerColor = BackgroundDark
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Loading habits...",
                        color = TextPrimary
                    )
                }
            } else {
                FrozenPaneLayout(
                    habitsWithCompletions = uiState.habitsWithCompletions,
                    weekInfo = uiState.weekInfo,
                    onHabitClick = onNavigateToHabitDetails,
                    onCellClick = { habitId, habitType, date ->
                        mainViewModel.onCellClick(habitId, habitType, date)
                    }
                )
            }
        }
    }

    // Numerical input dialog for measurable habits
    android.util.Log.d("BugFix", "HomeScreen - Dialog state: isVisible=${uiState.showMeasurableDialog}, habitName=${uiState.measurableDialogHabitName}")
    android.util.Log.d("BugFix", "HomeScreen - Dialog details: habitId=${uiState.measurableDialogHabitId}, unit=${uiState.measurableDialogUnit}")

    NumericalInputDialog(
        isVisible = uiState.showMeasurableDialog,
        habitName = uiState.measurableDialogHabitName,
        unit = uiState.measurableDialogUnit,
        currentValue = uiState.measurableDialogCurrentValue,
        targetValue = uiState.measurableDialogTargetValue,
        targetType = uiState.measurableDialogTargetType,
        onValueChange = { value ->
            android.util.Log.d("BugFix", "Dialog value changed: $value")
            mainViewModel.updateMeasurableDialogValue(value)
        },
        onConfirm = { value ->
            android.util.Log.d("BugFix", "Dialog confirmed with value: $value")
            mainViewModel.saveMeasurableHabitCompletion(
                uiState.measurableDialogHabitId,
                uiState.measurableDialogDate,
                value
            )
        },
        onDismiss = {
            android.util.Log.d("BugFix", "Dialog dismissed")
            mainViewModel.hideMeasurableHabitDialog()
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomHeader(
    currentSortType: com.example.habits9.data.HabitSortType,
    onAddHabitClick: () -> Unit = {},
    onSettingsClick: () -> Unit = {},
    onManageSectionsClick: () -> Unit = {},
    onSortTypeSelected: (com.example.habits9.data.HabitSortType) -> Unit = {}
) {
    var showDropdownMenu by remember { mutableStateOf(false) }
    
    TopAppBar(
        title = {
            Text(
                text = "Habits",
                color = TextPrimary,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = FontFamily.Default
            )
        },
        actions = {
            // Sort menu button
            SortMenuButton(
                currentSortType = currentSortType,
                onSortTypeSelected = onSortTypeSelected
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Add habit button
            IconButton(
                onClick = onAddHabitClick,
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = AccentPrimary,
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add Habit",
                    tint = BackgroundDark,
                    modifier = Modifier.size(20.dp)
                )
            }

            Spacer(modifier = Modifier.width(8.dp))
            
            // Settings/More options button with dropdown
            Box {
                IconButton(
                    onClick = { showDropdownMenu = true },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "More Options",
                        tint = TextSecondary,
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                DropdownMenu(
                    expanded = showDropdownMenu,
                    onDismissRequest = { showDropdownMenu = false },
                    modifier = Modifier.background(SurfaceVariantDark)
                ) {
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = "Manage Sections",
                                color = TextPrimary
                            )
                        },
                        onClick = {
                            showDropdownMenu = false
                            onManageSectionsClick()
                        }
                    )
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = "Settings",
                                color = TextPrimary
                            )
                        },
                        onClick = {
                            showDropdownMenu = false
                            onSettingsClick()
                        }
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = SurfaceVariantDark
        )
    )
}


@Composable
fun FrozenPaneLayout(
    habitsWithCompletions: List<com.example.habits9.ui.HabitWithCompletions>,
    weekInfo: com.example.habits9.ui.WeekInfo,
    onHabitClick: (Long) -> Unit = {},
    onCellClick: (Long, com.example.habits9.data.HabitType, Long) -> Unit = { _, _, _ -> }
) {


    val horizontalScrollState = rememberScrollState()
    
    // Constants for layout
    val habitColumnWidth = 200.dp // Increased width to ensure full habit names are visible
    val dateColumnWidth = 44.dp
    val mainHeaderHeight = 40.dp // New main header
    val percentageRowHeight = 30.dp // Percentage row
    val dayRowHeight = 30.dp // Day of week row
    val dateRowHeight = 30.dp // Date number row
    val totalHeaderHeight = mainHeaderHeight + percentageRowHeight + dayRowHeight + dateRowHeight
    val rowHeight = 48.dp
    
    Box(modifier = Modifier.fillMaxSize()) {
        // New Modern Header Row
        ModernWeekHeader(
            weekInfo = weekInfo,
            onPreviousWeek = { mainViewModel.navigateToPreviousWeek() },
            onNextWeek = { mainViewModel.navigateToNextWeek() },
            modifier = Modifier
                .fillMaxWidth()
                .height(mainHeaderHeight)
                .background(SurfaceVariantDark)
                .padding(horizontal = 16.dp)
        )
        
        // Main scrollable content area (habits and completion circles)
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = totalHeaderHeight, start = habitColumnWidth)
        ) {
            itemsIndexed(
                items = habitsWithCompletions,
                key = { index, habitWithCompletions -> "main_${habitWithCompletions.habit.uuid}_$index" }
            ) { habitIndex, habitWithCompletions ->
                Row(
                    modifier = Modifier
                        .height(rowHeight)
                        .background(if (habitIndex % 2 == 0) Color.Transparent else SurfaceVariantDark.copy(alpha = 0.3f))
                        .horizontalScroll(horizontalScrollState)
                ) {
                    weekInfo.timestamps.forEachIndexed { dayIndex, timestamp ->
                        // Normalize timestamp to day start to match the database storage format
                        val dayLength = 24 * 60 * 60 * 1000L
                        val dayStart = (timestamp / dayLength) * dayLength

                        // Check if the habit is scheduled for this day
                        val isScheduled = habitWithCompletions.scheduledDays[dayStart] ?: false

                        // For measurable habits, check if target is met; for Yes/No habits, check boolean completion
                        val isCompleted = if (habitWithCompletions.habit.habitType == com.example.habits9.data.HabitType.NUMERICAL) {
                            val valueString = habitWithCompletions.completionValues[dayStart]
                            if (valueString.isNullOrEmpty()) {
                                false
                            } else {
                                val value = valueString.toDoubleOrNull() ?: 0.0
                                when (habitWithCompletions.habit.numericalHabitType) {
                                    com.example.habits9.data.NumericalHabitType.AT_LEAST ->
                                        value >= habitWithCompletions.habit.targetValue
                                    com.example.habits9.data.NumericalHabitType.AT_MOST ->
                                        value <= habitWithCompletions.habit.targetValue
                                }
                            }
                        } else {
                            habitWithCompletions.completions[dayStart] ?: false
                        }

                        // Get the actual value for display (for measurable habits)
                        val completionValue = if (habitWithCompletions.habit.habitType == com.example.habits9.data.HabitType.NUMERICAL) {
                            habitWithCompletions.completionValues[dayStart] ?: ""
                        } else {
                            ""
                        }
                        
                        Box(
                            modifier = Modifier
                                .width(dateColumnWidth)
                                .height(rowHeight)
                                .background(
                                    // Apply a subtle background for non-scheduled days
                                    if (!isScheduled) DividerColor.copy(alpha = 0.1f) else Color.Transparent
                                )
                                .drawBehind {
                                    // Draw right border
                                    drawLine(
                                        color = DividerColor,
                                        start = Offset(size.width, 0f),
                                        end = Offset(size.width, size.height),
                                        strokeWidth = 1.dp.toPx()
                                    )
                                    // Draw bottom border
                                    drawLine(
                                        color = DividerColor,
                                        start = Offset(0f, size.height),
                                        end = Offset(size.width, size.height),
                                        strokeWidth = 1.dp.toPx()
                                    )
                                }
                                .clickable(enabled = isScheduled) {
                                    // New unified logic - call onCellClick with habit info
                                    if (isScheduled) {
                                        onCellClick(
                                            habitWithCompletions.habit.id,
                                            habitWithCompletions.habit.habitType,
                                            dayStart
                                        )
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            CompletionIndicator(
                                completed = isCompleted,
                                value = completionValue,
                                isMeasurable = habitWithCompletions.habit.habitType == com.example.habits9.data.HabitType.NUMERICAL,
                                isScheduled = isScheduled
                            )
                        }
                    }
                }
            }
        }
        
        // Note: Old date header rows removed as per requirements
        // The new ModernWeekHeader above replaces the old horizontal date list


        
        // Fixed left column (habit names and streaks)
        LazyColumn(
            modifier = Modifier
                .width(habitColumnWidth)
                .padding(top = mainHeaderHeight) // Only need padding for the main header now
                .background(SurfaceVariantDark)
        ) {
            itemsIndexed(
                items = habitsWithCompletions,
                key = { index, habitWithCompletions -> "left_${habitWithCompletions.habit.uuid}_$index" }
            ) { habitIndex, habitWithCompletions ->
                Row(
                    modifier = Modifier
                        .height(rowHeight)
                        .background(if (habitIndex % 2 == 0) Color.Transparent else SurfaceVariantDark.copy(alpha = 0.3f))
                        .drawBehind {
                            // Draw right border
                            drawLine(
                                color = DividerColor,
                                start = Offset(size.width, 0f),
                                end = Offset(size.width, size.height),
                                strokeWidth = 1.dp.toPx()
                            )
                            // Draw bottom border
                            drawLine(
                                color = DividerColor,
                                start = Offset(0f, size.height),
                                end = Offset(size.width, size.height),
                                strokeWidth = 1.dp.toPx()
                            )
                        }
                        .clickable { onHabitClick(habitWithCompletions.habit.id) }
                        .padding(horizontal = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Habit section color bar
                    Box(
                        modifier = Modifier
                            .width(3.dp)
                            .height(24.dp)
                            .background(AccentPrimary) // TODO: Use actual section color
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // Habit name and target text
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = habitWithCompletions.habit.name,
                            color = TextPrimary,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Normal,
                            maxLines = 2, // Allow 2 lines for longer habit names
                            lineHeight = 14.sp // Compact line height
                        )

                        // Target text for measurable habits (only show target, not current value)
                        if (habitWithCompletions.habit.habitType == com.example.habits9.data.HabitType.NUMERICAL) {
                            val targetTypeText = when (habitWithCompletions.habit.numericalHabitType) {
                                com.example.habits9.data.NumericalHabitType.AT_LEAST -> "at least"
                                com.example.habits9.data.NumericalHabitType.AT_MOST -> "at most"
                            }
                            val targetValue = if (habitWithCompletions.habit.targetValue == habitWithCompletions.habit.targetValue.toInt().toDouble()) {
                                habitWithCompletions.habit.targetValue.toInt().toString()
                            } else {
                                habitWithCompletions.habit.targetValue.toString()
                            }
                            val unit = if (habitWithCompletions.habit.unit.isNotEmpty()) " ${habitWithCompletions.habit.unit}" else ""
                            val targetText = "$targetTypeText $targetValue$unit"

                            Text(
                                text = targetText,
                                color = TextSecondary,
                                fontSize = 10.sp,
                                fontWeight = FontWeight.Normal,
                                maxLines = 1
                            )
                        }
                    }

                    // Streak counter positioned on the right
                    if (habitWithCompletions.currentStreak > 1) {
                        Text(
                            text = "\uD83D\uDD25 ${habitWithCompletions.currentStreak}",
                            color = TextSecondary,
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Normal,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CompletionIndicator(
    completed: Boolean,
    value: String = "",
    isMeasurable: Boolean = false,
    isScheduled: Boolean = true
) {
    // Apply visual styling based on scheduling status
    val alpha = if (isScheduled) 1f else 0.3f
    val disabledColor = DividerColor.copy(alpha = 0.5f)

    if (isMeasurable) {
        // For measurable habits, show only plain text (no background boxes)
        if (value.isNotEmpty() && value != "0") {
            Text(
                text = if (value.length > 3) "${value.take(2)}…" else value,
                color = if (!isScheduled) disabledColor
                       else if (completed) AccentPrimary
                       else TextSecondary,
                fontSize = 8.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.alpha(alpha)
            )
        } else {
            // Empty measurable habit - show dash or disabled indicator
            Text(
                text = if (isScheduled) "-" else "×",
                color = if (isScheduled) TextSecondary else disabledColor,
                fontSize = 8.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.alpha(alpha)
            )
        }
    } else if (!isScheduled) {
        // Non-scheduled Yes/No habits - show disabled indicator
        Canvas(
            modifier = Modifier
                .size(18.dp)
                .alpha(alpha)
        ) {
            drawCircle(
                color = disabledColor,
                radius = size.minDimension / 2,
                style = Stroke(width = 1.dp.toPx())
            )
            // Draw an X to indicate disabled
            val strokeWidth = 1.dp.toPx()
            val radius = size.minDimension / 2
            val offset = radius * 0.5f
            drawLine(
                color = disabledColor,
                start = Offset(center.x - offset, center.y - offset),
                end = Offset(center.x + offset, center.y + offset),
                strokeWidth = strokeWidth
            )
            drawLine(
                color = disabledColor,
                start = Offset(center.x - offset, center.y + offset),
                end = Offset(center.x + offset, center.y - offset),
                strokeWidth = strokeWidth
            )
        }
    } else if (completed) {
        // Filled circle for completed Yes/No habits
        Canvas(
            modifier = Modifier.size(18.dp)
        ) {
            drawCircle(
                color = AccentPrimary,
                radius = size.minDimension / 2
            )
        }
    } else {
        // Outlined circle for pending Yes/No habits
        Canvas(
            modifier = Modifier.size(18.dp)
        ) {
            drawCircle(
                color = TextSecondary,
                radius = size.minDimension / 2,
                style = Stroke(width = 1.dp.toPx())
            )
        }
    }
}

@RequiresApi(Build.VERSION_CODES.O)
private fun isWeekStartDay(date: LocalDate, firstDayOfWeek: String = "SUNDAY"): Boolean {
    return if (firstDayOfWeek == "SUNDAY") {
        date.dayOfWeek == DayOfWeek.SUNDAY
    } else {
        date.dayOfWeek == DayOfWeek.MONDAY
    }
}

@Composable
fun ModernWeekHeader(
    weekInfo: com.example.habits9.ui.WeekInfo,
    onPreviousWeek: () -> Unit,
    onNextWeek: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left side: "This Week" title and circular progress indicator
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "This Week",
                color = TextPrimary,
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                fontFamily = FontFamily.Default
            )

            CircularProgressIndicator(
                weeklyCompletionPercentage = weekInfo.weeklyCompletionPercentage
            )
        }

        // Right side: Week navigation
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            IconButton(
                onClick = onPreviousWeek,
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowLeft,
                    contentDescription = "Previous week",
                    tint = TextSecondary,
                    modifier = Modifier.size(20.dp)
                )
            }

            Text(
                text = "Week ${weekInfo.weekNumber}",
                color = TextSecondary,
                fontSize = 10.sp,
                fontWeight = FontWeight.Normal,
                fontFamily = FontFamily.Monospace
            )

            IconButton(
                onClick = onNextWeek,
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "Next week",
                    tint = TextSecondary,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun CircularProgressIndicator(
    weeklyCompletionPercentage: Float,
    modifier: Modifier = Modifier
) {
    val percentage = (weeklyCompletionPercentage * 100).toInt()

    Box(
        modifier = modifier.size(32.dp),
        contentAlignment = Alignment.Center
    ) {
        // Background circle
        Canvas(modifier = Modifier.fillMaxSize()) {
            drawCircle(
                color = DividerColor,
                radius = size.minDimension / 2,
                style = Stroke(width = 3.dp.toPx())
            )
        }

        // Progress arc
        Canvas(modifier = Modifier.fillMaxSize()) {
            val sweepAngle = (weeklyCompletionPercentage * 360f)
            drawArc(
                color = AccentPrimary,
                startAngle = -90f, // Start from top
                sweepAngle = sweepAngle,
                useCenter = false,
                style = Stroke(width = 3.dp.toPx(), cap = StrokeCap.Round)
            )
        }

        // Percentage text in center
        Text(
            text = "${percentage}%",
            color = TextPrimary,
            fontSize = 8.sp,
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily.Monospace
        )
    }
}